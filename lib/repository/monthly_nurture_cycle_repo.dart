import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';

class MonthlyNurtureCycleRepository {
  // 查询某个植物的所有养护类型的月份周期
  Future<FlowerMonthlyCycles> getFlowerMonthlyCycles(int flowerId) async {
    final records = await FlowerMonthlyNurtureCycle.getByFlower(flowerId);

    final Map<int, List<int>> typesCyclesMap = {};

    // 一次循环处理所有数据
    for (final record in records) {
      final typeId = record.typeId;
      final month = record.month;
      final cycle = record.cycle;

      // 确保月份在有效范围内
      if (month >= 1 && month <= 12) {
        // 如果该养护类型还没有初始化，创建12个月的默认数据
        if (!typesCyclesMap.containsKey(typeId)) {
          typesCyclesMap[typeId] = List.filled(12, -1);
        }

        // 直接设置对应月份的周期值
        typesCyclesMap[typeId]![month - 1] = cycle;
      }
    }

    // 转换为最终的数据结构
    final Map<int, MonthlyCycleData> typesCycles = {};
    for (final entry in typesCyclesMap.entries) {
      typesCycles[entry.key] = MonthlyCycleData(cycles: entry.value);
    }

    return FlowerMonthlyCycles(typesCycles: typesCycles);
  }

  // 查询某个植物的某种养护类型的月份周期
  Future<MonthlyCycleData> getFlowerTypeMonthlyData(int flowerId, int typeId) async {
    final records = await FlowerMonthlyNurtureCycle.getByFlowerAndType(flowerId, typeId);

    // 初始化12个月的数据为未设置(-1)
    final List<int> cycles = List.filled(12, -1);

    // 填充实际数据
    for (final record in records) {
      if (record.month >= 1 && record.month <= 12) {
        cycles[record.month - 1] = record.cycle;
      }
    }

    return MonthlyCycleData(cycles: cycles);
  }

  // 保存某个植物的所有养护类型的月份周期
  Future<void> saveFlowerMonthlyCycles(int flowerId, FlowerMonthlyCycles flowerCycles) async {
    // 先删除现有数据
    await FlowerMonthlyNurtureCycle.deleteByFlower(flowerId);

    // 保存新数据
    for (final entry in flowerCycles.typesCycles.entries) {
      final typeId = entry.key;
      final cycleData = entry.value;
      await FlowerMonthlyNurtureCycle.createBatch(flowerId, typeId, cycleData.cycles);
    }
  }

  // 保存某个植物的某种养护类型的月份周期
  Future<void> saveFlowerTypeMonthlyData(int flowerId, int typeId, MonthlyCycleData cycleData) async {
    await FlowerMonthlyNurtureCycle.updateBatch(flowerId, typeId, cycleData.cycles);
  }

  // 删除某个植物的所有月份周期数据
  Future<void> deleteFlowerMonthlyCycles(int flowerId) async {
    await FlowerMonthlyNurtureCycle.deleteByFlower(flowerId);
  }

  // 删除某个养护类型的所有月份周期数据
  Future<void> deleteNurtureTypeMonthlyCycles(int nurtureTypeId) async {
    await FlowerMonthlyNurtureCycle.deleteByNurtureType(nurtureTypeId);
  }

  // 从旧的养护周期数据迁移到新的月份周期数据
  Future<void> migrateFromLegacyCycles(int flowerId, Map<int, int> legacyCycles) async {
    final flowerCycles = FlowerMonthlyCycles.fromLegacyCycles(legacyCycles);
    await saveFlowerMonthlyCycles(flowerId, flowerCycles);
  }
}

// Riverpod Provider
final monthlyNurtureCycleRepositoryProvider = Provider<MonthlyNurtureCycleRepository>((ref) {
  return MonthlyNurtureCycleRepository();
});
